{"actions": [{"action": "export", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-share-alt", "id": "01564abb5fd08a8ae61a564409bc074a", "key": "export", "legend": "", "list": "", "method": "POST", "name": "导出", "primary": false, "rule": "", "selected": "none", "view": ""}, {"action": "", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"finish\",\"contact\"]},\"then\":{\"bpm_status\":\"contact\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-phone-alt", "id": "01543747a7398a8ae61a538908b40ca0", "key": "", "legend": "", "list": "waste_id,package_type,security_measure,outer_goal,handle_type,five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "联系外运", "primary": true, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-inbox", "id": "********************************", "key": "recontact", "legend": "再次派车", "list": "five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "再次派车", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-change-transfer", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-random", "id": "0156d9eca4338a8ae61a56d6183402e0", "key": "change", "legend": "", "list": "", "method": "POST", "name": "固废明细转单", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"contact\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-road", "id": "015726c016848a8ae61a57225de50251", "key": "send-to-lms", "legend": "", "list": "", "method": "POST", "name": "发送派车信息", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"out-store\",\"weight\",\"print\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-import", "id": "015591151ef98a8ae61a558c392b0343", "key": "weight", "legend": "", "list": "weight_record_code,weight,is_send_info,five_bills_code", "method": "PUT", "name": "过磅", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-file", "id": "01545655a2548a8ae61a538908b42029", "key": "print", "legend": "", "list": "", "method": "POST", "name": "联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-out", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-file", "id": "01555d33c84c8a8ae61a555d0e3b002b", "key": "print-n-out", "legend": "", "list": "", "method": "POST", "name": "宁波省外联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-in", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-file", "id": "01555d35c97e8a8ae61a555d0e3b0033", "key": "print-n-in", "legend": "", "list": "", "method": "POST", "name": "宁波省内联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "file", "actionParam": "{\"before\":{\"and\":{\"bpm_status\":[\"print\"]}},\"update\":{\"if\":{\"bpm_status\":[\"print\"]},\"then\":{\"bpm_status\":\"upload\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-plus", "id": "015566f380048a8ae61a555d0e3b0a2a", "key": "upload", "legend": "", "list": "", "method": "POST", "name": "扫描件上传", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"finish\",\"upload\"]},\"then\":{\"bpm_status\":\"close\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-remove-sign", "id": "01559ac7f7408a8ae61a559ab4a80039", "key": "close", "legend": "", "list": "", "method": "POST", "name": "关闭", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"weight\",\"print\",\"upload\",\"close\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-refresh", "id": "01571d687aa88a8ae61a56fcd1e30c07", "key": "picture", "legend": "", "list": "", "method": "POST", "name": "获取过磅图片", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"contact\",\"out-store\",\"weight\",\"print\",\"upload\",\"close\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-remove", "id": "0159af7a51b88a8afbb85972e76701dd", "key": "clear-pls-info", "legend": "该操作会清空物流系统对应单号的派车信息，需地磅系统配合处理", "list": "", "method": "POST", "name": "清空派车信息", "primary": false, "rule": "", "selected": "single", "view": ""}], "data": {"collection": [{"transfer_id": "0159aefce3d38a8ae703596dfa3502f8", "apply_code": "201701180004", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484668800000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "5吨", "plan_transfer_time": 1484668800000, "transfer_time": 1484704760059, "duty_person": "王培福", "waste_id": "e55a937228e64d38a79de64cbfcead37", "waste_name": "粘有物料的废劳保、废抹布", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废弃物", "risk": "毒性", "waste_modal": "", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015778917dd88a8ae703577006b309da", "dispose_enterprise": "01563aee71898a8afbb4563608e500a1", "five_bills_code": "20170118-02", "transfer_person": "胡", "car_id": "0157df73009c8a8ae70457d04d1c1f46", "card_id": "2B134427", "card_code": "车卡04", "card_name": "万华宁波固废管理系统车卡04", "lock_id": "01583266db038a8ae70357e13fbf11db", "lock_code": "16430005", "transfer_start_position": "万华宁波", "transfer_end_position": "北仑固废", "weight_record_code": "20170118-0010", "weight": 4680, "note": "", "wait_count": "0", "pass_count": "298", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484699874797, "update_time": 1670409285099, "is_send_info": "1", "year": "2017"}, {"transfer_id": "017a46d6407d8a28f2277a36dc230662", "apply_code": "202106260004", "user_name": "徐巧琳", "org_id": "00000439", "org_name": "宁波设备部", "parent_org_id": "00000439", "parent_org_name": "宁波设备部", "hwms_org_id": "00000439", "apply_date": 1624636800000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "0535-642589", "plan_transfer_quantity": "2", "duty_person": "尹若明", "transfer_position": "暂存区", "five_bills_code": "", "note": "", "wait_count": "1", "pass_count": "0", "back_count": "0", "bpm_status": "finish", "bpm_update_time": 1624686730685, "update_time": 1624686084091, "is_send_info": "1", "year": "2021"}, {"transfer_id": "017954dd5dda8a28b2aa7954c2f5006f", "apply_code": "202105100006", "user_name": "吴银威", "org_id": "**********", "org_name": "MDI装置大横班乙班", "parent_org_id": "00000436", "parent_org_name": "宁波MDI装置", "hwms_org_id": "**********", "company_id": "00000017", "apply_date": 1620576000000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "642589", "plan_transfer_quantity": "2", "plan_transfer_time": 1620662400000, "duty_person": "尤红军", "transfer_position": "暂存区", "waste_id": "0157275198678a8ae704570e3d621f9f", "waste_name": "污水处理污泥", "category_code": "565-104-13", "parent_category_code": "HW13", "report_group_name": "污水处理污泥", "risk": "毒性", "waste_modal": "固态/液态", "harmful_ingredient": "磷酸钙,硅酸铝", "package_type": "桶装", "security_measure": "防火、防爆、防漏", "outer_goal": "处置", "handle_type": "3", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01575a100f718a8ae70457565eca0300", "dispose_enterprise": "0156695406328a8afbb3566926740034", "five_bills_code": "TEST202105100001", "transfer_person": "尹若明", "car_id": "0156789405208a8afbb45673e5b40110", "card_id": "2B18722C", "card_code": "车卡10_宁波容威", "card_name": "宁波容威车卡10", "lock_id": "", "transfer_start_position": "起点", "transfer_end_position": "终点", "note": "test", "wait_count": "2", "pass_count": "0", "back_count": "0", "bpm_status": "contact", "bpm_update_time": 1620627017587, "update_time": 1620627709695, "is_send_info": "1", "year": "2021", "division_id": "00000017"}, {"transfer_id": "0159aadaae038a8ae703596dfa357e70", "apply_code": "201701170011", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484582400000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "3吨", "plan_transfer_time": 1484582400000, "transfer_time": 1484723785519, "duty_person": "王培福", "waste_id": "c3ad81a834cb43ea9cce9ed2c5da419d", "waste_name": "空铁桶-200L", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "废包装桶", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "空桶", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01574bdf9cb08a8ae7045728183a3e7f", "dispose_enterprise": "0156695de37f8a8afbb3566926740053", "five_bills_code": "20170117-02", "transfer_person": "田", "car_id": "01575abaab608a8ae70457565eca1c54", "card_id": "2B135D03", "card_code": "车卡01", "card_name": "万华宁波固废管理系统车卡01", "lock_id": "", "transfer_start_position": "万华宁波", "transfer_end_position": "温州强成", "weight_record_code": "20170118-0036", "weight": 3140, "note": "空铁桶 120 空塑料桶70", "wait_count": "0", "pass_count": "190", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484633140211, "update_time": 1484788629586, "is_send_info": "1", "year": "2017"}, {"transfer_id": "01596ca758518a8ae704594ee5973382", "apply_code": "201701050007", "org_id": "*******************", "org_name": "仓库管理", "parent_org_id": "00000016", "parent_org_name": "万华化学（宁波）容威聚氨酯有限公司", "apply_date": 1483545600000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "66176", "plan_transfer_quantity": "190", "plan_transfer_time": 1484928000000, "duty_person": "薛晓杰", "transfer_position": "007仓库", "five_bills_code": "", "note": "", "wait_count": "190", "pass_count": "0", "back_count": "0", "bpm_status": "finish", "bpm_update_time": 1484813524222, "update_time": 1484788408540, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159b40f6f438a8ae703596dfa350f9d", "apply_code": "201701190003", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484755200000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66270", "plan_transfer_quantity": "8吨", "plan_transfer_time": 1484755200000, "transfer_time": 1484785410245, "duty_person": "王培福", "waste_id": "f4495d2625f446f38152819dc3e83dbf", "waste_name": "废活性炭", "category_code": "900-039-49", "parent_category_code": "HW49", "report_group_name": "废活性炭", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015778917dd88a8ae703577006b309da", "dispose_enterprise": "01563aee71898a8afbb4563608e500a1", "five_bills_code": "20170119-01", "transfer_person": "刘虎林", "car_id": "01586fa73d3e8a8ae704583e04c34a06", "card_id": "E5FD4AB4", "card_code": "车卡06", "card_name": "万华宁波固废管理系统车卡06", "lock_id": "", "transfer_start_position": "万华宁波", "transfer_end_position": "北仑固废", "note": "罐储", "wait_count": "0", "pass_count": "52", "back_count": "0", "bpm_status": "out-store", "bpm_update_time": 1484784974412, "update_time": 1484785124059, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159a470bfc08a8ae704596df9896925", "apply_code": "201701160001", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484496000000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "6吨", "plan_transfer_time": 1484496000000, "transfer_time": 1484619965152, "duty_person": "王培福", "waste_id": "e55a937228e64d38a79de64cbfcead37", "waste_name": "粘有物料的废劳保、废抹布", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废弃物", "risk": "毒性", "waste_modal": "", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015778917dd88a8ae703577006b309da", "dispose_enterprise": "01563aee71898a8afbb4563608e500a1", "five_bills_code": "20170117-01", "transfer_person": "胡", "car_id": "0158229cddc98a8ae70457e1412571f6", "card_id": "A5C79A11", "card_code": "车卡03", "card_name": "万华宁波固废管理系统车卡03", "lock_id": "0156f824bbf88a8ae70456dacfa52d58", "lock_code": "16300013", "transfer_start_position": "万华宁波", "transfer_end_position": "北仑固废", "weight_record_code": "20170117-0013", "weight": 7020, "note": "有毒 有害", "wait_count": "0", "pass_count": "364", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484526026448, "update_time": 1484709829399, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159af0ba42b8a8ae703596dfa35043f", "apply_code": "201701180007", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484668800000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "12吨", "plan_transfer_time": 1484668800000, "transfer_time": 1484701586871, "duty_person": "王培福", "waste_id": "015727db7bb68a8ae703570e3cee2435", "waste_name": "轻组分废液", "category_code": "900-999-49", "parent_category_code": "HW49", "report_group_name": "轻组分废液", "risk": "毒性", "waste_modal": "液态", "harmful_ingredient": "", "package_type": "桶装", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01575a100f718a8ae70457565eca0300", "dispose_enterprise": "0156694dc2458a8afbb45659c47e183d", "five_bills_code": "20170118-01", "transfer_person": "姚", "car_id": "0157552b11f98a8ae70357282fd57300", "card_id": "2B1313AC", "card_code": "车卡02", "card_name": "万华宁波固废管理系统车卡02", "lock_id": "01583266a4788a8ae70357e13fbf11d9", "lock_code": "16430004", "transfer_start_position": "万华宁波", "transfer_end_position": "大地环保", "weight_record_code": "20170118-0002", "weight": 13080, "note": "HMDI", "wait_count": "0", "pass_count": "12", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484700668194, "update_time": 1484709710807, "is_send_info": "1", "year": "2017"}, {"transfer_id": "01599f4ce0c68a8ae703596dfa356790", "apply_code": "201701150003", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484409600000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "3.6吨", "plan_transfer_time": 1484409600000, "transfer_time": 1484527175326, "duty_person": "王培福", "waste_id": "c3ad81a834cb43ea9cce9ed2c5da419d", "waste_name": "空铁桶-200L", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "废包装桶", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "空桶", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01574bdf9cb08a8ae7045728183a3e7f", "dispose_enterprise": "0156695de37f8a8afbb3566926740053", "five_bills_code": "20170116-02", "transfer_person": "田", "car_id": "01574bdc4d048a8ae7045728183a3e10", "card_id": "2B135D03", "card_code": "车卡01", "card_name": "万华宁波固废管理系统车卡01", "lock_id": "0156f8247a6b8a8ae70456dacfa52d53", "lock_code": "16300011", "transfer_start_position": "万华宁波", "transfer_end_position": "温州强成", "weight_record_code": "20170116-0010", "weight": 3460, "note": "有毒 有害", "wait_count": "0", "pass_count": "190", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484440007643, "update_time": 1484546407361, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159a59f51b68a8ae703596dfa356f29", "apply_code": "201701160016", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484496000000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "30吨", "plan_transfer_time": 1484496000000, "transfer_time": 1484543362811, "duty_person": "王培福", "waste_id": "b59884ed70ec44f1bc2fccc417b68521", "waste_name": "苯胺焦油", "category_code": "261-019-11", "parent_category_code": "HW11", "report_group_name": "苯胺焦油", "risk": "毒性", "waste_modal": "固态/液态", "harmful_ingredient": "苯胺,环己胺", "package_type": "槽罐", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015750c250cf8a8ae70357282fd55f96", "dispose_enterprise": "0156695406328a8afbb3566926740034", "five_bills_code": "20170116-01", "transfer_person": "钱俊", "car_id": "015750d8dd5f8a8ae70357282fd5609f", "card_id": "2B1313AC", "card_code": "车卡02", "card_name": "万华宁波固废管理系统车卡02", "lock_id": "0156f8249c3c8a8ae70456dacfa52d56", "lock_code": "16300012", "transfer_start_position": "万华宁波", "transfer_end_position": "宜兴麦克", "weight_record_code": "29170116-000021", "weight": 28540, "note": "", "wait_count": "0", "pass_count": "1", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484543046448, "update_time": 1484546300040, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159903721ff8a8ae704596df9894fb4", "apply_code": "201701120005", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484150400000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "8吨", "plan_transfer_time": 1484150400000, "transfer_time": 1484186122374, "duty_person": "王培福", "waste_id": "f4495d2625f446f38152819dc3e83dbf", "waste_name": "废活性炭", "category_code": "900-039-49", "parent_category_code": "HW49", "report_group_name": "废活性炭", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015778917dd88a8ae703577006b309da", "dispose_enterprise": "01563aee71898a8afbb4563608e500a1", "five_bills_code": "20170112-01", "transfer_person": "胡", "car_id": "0157df73009c8a8ae70457d04d1c1f46", "card_id": "2B134427", "card_code": "车卡04", "card_name": "万华宁波固废管理系统车卡04", "lock_id": "0156f824bbf88a8ae70456dacfa52d58", "lock_code": "16300013", "transfer_start_position": "万华宁波", "transfer_end_position": "北仑固废", "weight_record_code": "20170112-0021", "weight": 5820, "note": "罐储工序", "wait_count": "0", "pass_count": "36", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484184868296, "update_time": 1484447886915, "is_send_info": "1", "year": "2017"}, {"transfer_id": "01598adcef688a8ae703596dfa353023", "apply_code": "201701110004", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484064000000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "3.8吨", "plan_transfer_time": 1484064000000, "transfer_time": 1484100827509, "duty_person": "王培福", "waste_id": "015774c4b7598a8ae704577008a9076a", "waste_name": "废多元醇桶", "category_code": "900-999-49", "parent_category_code": "HW49", "report_group_name": "废包装桶", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "多元醇", "package_type": "空桶", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01574bdf9cb08a8ae7045728183a3e7f", "dispose_enterprise": "0156695de37f8a8afbb3566926740053", "five_bills_code": "20170111-01", "transfer_person": "田", "car_id": "01574bdc4d048a8ae7045728183a3e10", "card_id": "2B135D03", "card_code": "车卡01", "card_name": "万华宁波固废管理系统车卡01", "lock_id": "01563b3630448a8afbb4563608e500da", "lock_code": "16300009", "transfer_start_position": "万华宁波", "transfer_end_position": "温州强成", "weight_record_code": "20170111-0047", "weight": 2680, "note": "有毒 有害", "wait_count": "0", "pass_count": "384", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484094205907, "update_time": 1484447331519, "is_send_info": "1", "year": "2017"}, {"transfer_id": "0159952448a48a8ae704596df989554b", "apply_code": "201701130003", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484236800000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "3.8吨", "plan_transfer_time": 1484236800000, "transfer_time": 1484288188514, "duty_person": "王培福", "waste_id": "015774c4b7598a8ae704577008a9076a", "waste_name": "废多元醇桶", "category_code": "900-999-49", "parent_category_code": "HW49", "report_group_name": "废包装桶", "risk": "毒性", "waste_modal": "固态", "harmful_ingredient": "多元醇", "package_type": "空桶", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "01574bdf9cb08a8ae7045728183a3e7f", "dispose_enterprise": "0156695de37f8a8afbb3566926740053", "five_bills_code": "20170113-01", "transfer_person": "田", "car_id": "01574bdc4d048a8ae7045728183a3e10", "card_id": "2B1313AC", "card_code": "车卡02", "card_name": "万华宁波固废管理系统车卡02", "lock_id": "", "transfer_start_position": "万华宁波", "transfer_end_position": "温州强成", "weight_record_code": "20170113-0049", "weight": 3940, "note": "有毒 有害", "wait_count": "0", "pass_count": "190", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484267643078, "update_time": 1484447216426, "is_send_info": "1", "year": "2017"}, {"transfer_id": "01598bb3385d8a8ae703596dfa3541a9", "apply_code": "201701110012", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484064000000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "30吨", "plan_transfer_time": 1484064000000, "transfer_time": 1484109088638, "duty_person": "王培福", "waste_id": "b59884ed70ec44f1bc2fccc417b68521", "waste_name": "苯胺焦油", "category_code": "261-019-11", "parent_category_code": "HW11", "report_group_name": "苯胺焦油", "risk": "毒性", "waste_modal": "固态/液态", "harmful_ingredient": "苯胺,环己胺", "package_type": "槽罐", "security_measure": "有毒有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015750c250cf8a8ae70357282fd55f96", "dispose_enterprise": "0156695406328a8afbb3566926740034", "five_bills_code": "20170111-04", "transfer_person": "钱", "car_id": "015750d8dd5f8a8ae70357282fd5609f", "card_id": "A5C79A11", "card_code": "车卡03", "card_name": "万华宁波固废管理系统车卡03", "lock_id": "0156f824e2468a8ae70456dacfa52d5a", "lock_code": "16300014", "transfer_start_position": "万华宁波", "transfer_end_position": "宜兴迈克", "weight_record_code": "20170111-0020", "weight": 28680, "note": "", "wait_count": "0", "pass_count": "1", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484108722540, "update_time": 1484446992940, "is_send_info": "1", "year": "2017"}, {"transfer_id": "01598ad682d18a8ae703596dfa35301f", "apply_code": "201701110003", "user_name": "王培福", "org_id": "00000442", "org_name": "宁波联合装置", "parent_org_id": "**********", "parent_org_name": "生产体系", "apply_date": 1484064000000, "apply_type": "station", "is_plan": "", "transfer_type": "outer", "is_sales": "0", "phone": "66271", "plan_transfer_quantity": "6吨", "plan_transfer_time": 1484064000000, "transfer_time": 1484273186428, "duty_person": "王培福", "waste_id": "e55a937228e64d38a79de64cbfcead37", "waste_name": "粘有物料的废劳保、废抹布", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废弃物", "risk": "毒性", "waste_modal": "", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "有毒 有害", "outer_goal": "处置", "produce_enterprise": "0157089194828a8ae70456f9ad0710a3", "transfer_enterprise": "015778917dd88a8ae703577006b309da", "dispose_enterprise": "01563aee71898a8afbb4563608e500a1", "five_bills_code": "20170112001", "transfer_person": "胡", "car_id": "0158a935116b8a8ae703589a9f5807cc", "card_id": "E5FD4AB4", "card_code": "车卡06", "card_name": "万华宁波固废管理系统车卡06", "lock_id": "01583265f9c08a8ae70357e13fbf11d4", "lock_code": "16430002", "transfer_start_position": "万华宁波", "transfer_end_position": "北仑固废", "weight_record_code": "20170111-0015", "weight": 6280, "note": "有毒 有害", "wait_count": "0", "pass_count": "442", "back_count": "0", "bpm_status": "weight", "bpm_update_time": 1484094232866, "update_time": 1484446927688, "is_send_info": "1", "year": "2017"}], "count": 186, "exists": false, "pagination": {"count": 13, "current": 1, "paging": true, "size": 15}}, "extend": "", "handle": "mulpitle", "icon": "", "id": "01543747a6c88a8ae61a538908b40c86", "layout": "", "legend": "视图", "mode": "table", "name": "固废外运处置（全部）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "转移主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请单号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "基础信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "apply_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "user_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "工序", "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "申请单工序", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "00000016", "org_name": "万华化学（宁波）容威聚氨酯有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}, {"_childList": [{"_childList": [{"org_alias": "", "org_id": "00000439", "org_name": "宁波设备部", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"org_alias": "", "org_id": "00000442", "org_name": "宁波联合装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置大横班乙班", "org_stitle": "", "parent_org_id": "00000436", "status": ""}], "org_alias": "", "org_id": "00000436", "org_name": "宁波MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "生产体系", "org_stitle": "", "parent_org_id": "00000017", "status": ""}], "org_alias": "", "org_id": "00000017", "org_name": "万华化学（宁波）有限公司", "org_stitle": "", "parent_org_id": "", "sort_num": 130, "status": ""}, {"org_alias": "", "org_id": "*******************", "org_name": "仓库管理", "org_stitle": "", "parent_org_id": "00000424", "status": ""}], "count": 8, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "00000434", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "工序", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "装置", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "申请单装置", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "00000016", "org_name": "万华化学（宁波）容威聚氨酯有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}, {"_childList": [{"_childList": [{"org_alias": "", "org_id": "00000439", "org_name": "宁波设备部", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"org_alias": "", "org_id": "00000442", "org_name": "宁波联合装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置大横班乙班", "org_stitle": "", "parent_org_id": "00000436", "status": ""}], "org_alias": "", "org_id": "00000436", "org_name": "宁波MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "生产体系", "org_stitle": "", "parent_org_id": "00000017", "status": ""}], "org_alias": "", "org_id": "00000017", "org_name": "万华化学（宁波）有限公司", "org_stitle": "", "parent_org_id": "", "sort_num": 130, "status": ""}, {"org_alias": "", "org_id": "*******************", "org_name": "仓库管理", "org_stitle": "", "parent_org_id": "00000424", "status": ""}], "count": 8, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "装置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "组织", "column": 6, "control": "view-select", "dataType": "wordbook", "dictMode": "bydata", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "hwms_org_id", "params": {"control": {"displayValue": "org_name", "realValue": "org_id", "url": "/hwms/api/hwms-org-select"}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置大横班乙班", "org_stitle": "", "parent_org_id": "00000436", "status": ""}, {"org_alias": "", "org_id": "00000439", "org_name": "宁波设备部", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "count": 2, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "01626be7b4838a8afbb86266191b01e8", "layout": "", "legend": "", "mode": "tree", "name": "HWMS虚拟组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "control": "", "dataType": "uuid", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "control": "text", "dataType": "string", "display": "title", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "control": "", "dataType": "wordbook", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "control": "text", "dataType": "integer", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "control": "select", "dataType": "string", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属公司", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "company_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_id": "00000017", "org_name": "万华化学（宁波）有限公司"}], "count": 1, "exists": false}, "extend": "", "handle": "mulpitle", "icon": "", "id": "017862d0a8948a28483d7862af7f002a", "layout": "", "legend": "HWMS查询类型为公司的组织机构", "mode": "table", "name": "HWMS公司字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "00000017", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请日期", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "apply_date", "remind": "", "required": true, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "1756224000000", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废转移", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "equ:装置,station:固废站", "maxLength": 64, "metaType": "String", "name": "apply_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "是否计划内", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_plan", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "inner:转移至固废站,outer:外运", "maxLength": 64, "metaType": "String", "name": "transfer_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "内部转移", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "处置去向", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "0:直接外委处置,1:有价值处置,2:环保科技处置", "maxLength": 64, "metaType": "String", "name": "is_sales", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "0", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "联系电话", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "phone", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移量", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "plan_transfer_quantity", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移时间", "control": "datetime", "dataType": "date", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "plan_transfer_time", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际转移时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "transfer_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移联系人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "duty_person", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际操作人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "operator", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "废物产生位置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "column": 6, "control": "view-select", "dataType": "wordbook", "dictMode": "bydata", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "params": {"control": {"url": "/hwms/api/chosen-waste-name-all"}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [{"group": "1", "icon": "glyphicon-plus", "id": "01783f6cb81f8a28a07d783f3926005d", "key": "", "method": "POST", "name": "新增", "primary": true, "selected": "none"}, {"group": "1", "icon": "glyphicon-pencil", "id": "01783f6cb8218a28a07d783f3926005e", "key": "", "method": "PUT", "name": "修改", "primary": false, "selected": "single"}, {"group": "", "icon": "glyphicon-trash", "id": "01783f6cb8238a28a07d783f3926005f", "key": "", "method": "DELETE", "name": "删除", "primary": false, "selected": "somewhat"}], "data": {"collection": [{"category_id": "01598271133e8a8ae703596dfa3528e0", "is_key_waste": "1", "report_group_id": "0159866e4cf48a8ae704596df9892c6f", "status": "1", "waste_id": "0157275198678a8ae704570e3d621f9f", "waste_name": "污水处理污泥", "waste_source": "宁波容威装置污水预物化处理生产污泥"}, {"category_id": "0156f94b4bfb8a8ae70356dacef629f2", "is_key_waste": "1", "report_group_id": "015727dc693c8a8ae703570e3cee2437", "status": "1", "waste_id": "015727db7bb68a8ae703570e3cee2435", "waste_name": "轻组分废液", "waste_source": "ADI事业部"}, {"category_id": "0156f94b4bfb8a8ae70356dacef629f2", "is_key_waste": "0", "report_group_id": "db7af02518ab4131ba681b068c6d292a", "status": "1", "waste_id": "015774c4b7598a8ae704577008a9076a", "waste_name": "废多元醇桶", "waste_source": "水性装置"}, {"category_id": "015672549acc8a8ae7045667b16d04a9", "is_key_waste": "1", "report_group_id": "015727e6a9388a8ae703570e3cee2452", "sort_num": 12, "status": "1", "waste_id": "b59884ed70ec44f1bc2fccc417b68521", "waste_name": "苯胺焦油", "waste_source": "苯胺装置"}, {"category_id": "0156726a40978a8ae7045667b16d04cc", "is_key_waste": "", "report_group_id": "2139bd5439cd478b81979f0ff94491dc", "sort_num": 40, "status": "1", "waste_id": "f4495d2625f446f38152819dc3e83dbf", "waste_name": "废活性炭", "waste_source": ""}, {"category_id": "0156726c3e508a8ae7045667b16d04cf", "is_key_waste": "", "report_group_id": "ff8150e1d7f947c9a8d099f02829cc4a", "sort_num": 45, "status": "1", "waste_id": "e55a937228e64d38a79de64cbfcead37", "waste_name": "粘有物料的废劳保、废抹布", "waste_source": ""}, {"category_id": "0156726c3e508a8ae7045667b16d04cf", "is_key_waste": "", "report_group_id": "db7af02518ab4131ba681b068c6d292a", "sort_num": 50, "status": "1", "waste_id": "c3ad81a834cb43ea9cce9ed2c5da419d", "waste_name": "空铁桶-200L", "waste_source": ""}], "count": 7, "exists": false}, "extend": "", "handle": "mulpitle", "icon": "", "id": "01783f6cb7b68a28a07d783f39260053", "layout": "", "legend": "视图", "mode": "table", "name": "固废名称视图选择器（所有）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "固废主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分类", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "category_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分组", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "report_group_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废来源", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_source", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "是否重点关注固废", "column": 6, "control": "select", "dataType": "string", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_key_waste", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "1:有效,0:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "序号", "control": "text", "dataType": "integer", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "固废名称", "control": "text", "name": "waste_name", "required": false, "value": ""}, {"alias": "固废分类", "control": "select", "name": "category_id", "required": false, "value": ""}, {"alias": "固废分组", "control": "select", "name": "report_group_id", "required": false, "value": ""}], "idName": "waste_id", "nodeIdName": "waste_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废代码", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废类别", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "报批分组", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "report_group_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "危险特性", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "毒性,腐蚀性,易燃性,反应性,感染性", "maxLength": 64, "metaType": "String", "name": "risk", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废形态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_modal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "主要有害成分", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "harmful_ingredient", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "包装方式", "column": 6, "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "袋装,桶装,箱装,空桶,槽罐,管道运输,其它", "maxLength": 64, "metaType": "String", "name": "package_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "禁忌与应急措施", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "security_measure", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "防火、防爆、防漏", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "外运目的", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "外运信息", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "中转贮存,利用,处理,处置,其他", "maxLength": 64, "metaType": "String", "name": "outer_goal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "处置", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物处置方式", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "20:D1-填埋,21:D9-物理化学处理,22:D10-焚烧,23:R2-溶剂回收/再生（如蒸馏、萃取等）,24:R3-再循环/再利用不是用作溶剂的有机物,25:R4-再循环/再利用金属和金属化合物,26:R5-再循环/再利用其他无机物,27:R6-再生酸或碱,28:R7-回收污染减除剂的组分,29:R8-回收催化剂组分,30:R9-废油再提炼或其他废油的再利用,31:R15-其他,32:C1-水泥窑共处置,33:C5-收集废物", "maxLength": 64, "metaType": "String", "name": "handle_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "产生企业", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "produce_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "0157089194828a8ae70456f9ad0710a3", "enterprise_name": "万华化学（宁波）有限公司", "enterprise_property": "国有", "enterprise_type": "produce", "industry_code": ""}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd439080e8a28906d6bcf6b81001f", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（产生企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输企业", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "01574bdf9cb08a8ae7045728183a3e7f", "enterprise_name": "永嘉县长顺危险化学品货物运输有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": ""}, {"enterprise_id": "015750c250cf8a8ae70357282fd55f96", "enterprise_name": "江阴市腾飞物流有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": ""}, {"enterprise_id": "01575a100f718a8ae70457565eca0300", "enterprise_name": "宁波安自富国际物流有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": ""}, {"enterprise_id": "015778917dd88a8ae703577006b309da", "enterprise_name": "宁波腾业化工物流有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": ""}], "count": 4, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4394bd88a28906d6bcf6b810031", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（运输企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "接受单位", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "dispose_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "01563aee71898a8afbb4563608e500a1", "enterprise_name": "宁波市北仑环保固废处置有限公司", "enterprise_property": "国有", "enterprise_type": "dispose", "industry_code": "危险废物处理"}, {"enterprise_id": "0156694dc2458a8afbb45659c47e183d", "enterprise_name": "宁波大地化工环保有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}, {"enterprise_id": "0156695406328a8afbb3566926740034", "enterprise_name": "宜兴市迈克化工有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}, {"enterprise_id": "0156695de37f8a8afbb3566926740053", "enterprise_name": "温州强成环保科技有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}], "count": 4, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4391e7e8a28906d6bcf6b810028", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（处置企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "五联单号", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "山东省内转移无需填写", "list": "", "maxLength": 64, "metaType": "String", "name": "five_bills_code", "params": {"control": {"events": {"change:dispose_enterprise": "hide|01783ddecaea8a87d89c783ddab70005", "change:produce_enterprise": "hide|01783ddecaea8a87d89c783ddab70005"}}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "承运人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_person", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物运输车牌号", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"car_code": "苏B96268", "car_id": "0156789405208a8afbb45673e5b40110", "car_type": "槽罐车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B1A863", "car_id": "01574bdc4d048a8ae7045728183a3e10", "car_type": "重型半挂牵引车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "苏BD0092", "car_id": "015750d8dd5f8a8ae70357282fd5609f", "car_type": "槽罐车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B99293", "car_id": "0157552b11f98a8ae70357282fd57300", "car_type": "中型卡车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "皖C63687", "car_id": "01575abaab608a8ae70457565eca1c54", "car_type": "大型卡车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B2A738", "car_id": "0157df73009c8a8ae70457d04d1c1f46", "car_type": "中型卡车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B3A018", "car_id": "0158229cddc98a8ae70457e1412571f6", "car_type": "中型卡车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B3A015", "car_id": "01586fa73d3e8a8ae704583e04c34a06", "car_type": "中型卡车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}, {"car_code": "浙B3A028", "car_id": "0158a935116b8a8ae703589a9f5807cc", "car_type": "槽罐车", "note": "", "region_id": "0155723237938a8ae61a5570bbef10e4", "status": "有效", "transfer_code": ""}], "count": 9, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0154370cf0088a8ae61a538908b40bfe", "layout": "", "legend": "视图", "mode": "table", "name": "车辆字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "车辆主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车牌号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "car_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车辆类型", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "槽罐车,中型卡车,大型卡车,特级车辆,重型厢式货车,重型普通货车,重型仓栅式货车,重型半挂牵引车,管道输送", "maxLength": 64, "metaType": "String", "name": "car_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输证号", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "有效期", "control": "date", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "valid_date", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "hidden": "list", "i18nCode": "", "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "有效,无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "区域主键", "column": 6, "control": "", "dataType": "id", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "region_id", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "0155723237938a8ae61a5570bbef10e4", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "car_id", "nodeIdName": "car_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车卡", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [], "count": 0, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0155b4e445ce8a8ae61a55b07772058e", "layout": "", "legend": "IC卡信息视图", "mode": "table", "name": "可用车卡字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "<PERSON><PERSON>ly", "alias": "物理编号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "卡类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "car:车卡,person:员工卡", "maxLength": 64, "metaType": "String", "name": "card_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "有效期", "control": "date", "dataType": "date", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "card_validity", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "绑定对象", "control": "text", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_object", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "卡状态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "Y:是,N:否", "maxLength": 64, "metaType": "String", "name": "card_status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "N", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "card_id", "nodeIdName": "card_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "电子锁", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "lock_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [], "count": 0, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0155b4e6a4c78a8ae61a55b0777205a2", "layout": "", "legend": "视图", "mode": "table", "name": "可用电子锁字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "device_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "handset:手持机,scale:磅秤,lock:电子锁", "maxLength": 64, "metaType": "String", "name": "device_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备编码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_code", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_identifies", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "MAC", "control": "", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "mac", "remind": "FF:FF:FF:FF:FF:FF", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "chosen", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "valid:有效,invalid:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "valid", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "device_id", "nodeIdName": "device_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "电子锁", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "lock_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "电子锁回收确认时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "lock_recycle_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输起点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_start_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输终点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_end_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "过磅单号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "weight_record_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "皮重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "tare_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "毛重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "gross_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "净重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "first_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "second_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "待转移总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "wait_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "通过总数", "control": "", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "pass_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "退回总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "back_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\r\n\tvar style = 'style=\"color:red;font-weight:bold;\" ';    \r\n    var bpmStatus = row['bpm_status'];\r\n    if(bpmStatus == \"weight\" || bpmStatus == \"print\" || bpmStatus == \"upload\") {\r\n\t    var transferTime = row['transfer_time']; \r\n\t    style = ((typeof(transferTime) == \"undefined\") || (transferTime == ''))\r\n        ? style : '';\r\n\t    return '<span ' +style+ '>'+value+'</span>';\r\n\t} else {\r\n\t\treturn value;\r\n\t}    \r\n}", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "finish:审批完成,contact:联系外运,out-store:已出库,weight:已过磅,print:联单已打印,upload:联单已上传", "maxLength": 64, "metaType": "String", "name": "bpm_status", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "draft", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "BPM更新时间", "column": 6, "control": "text", "dataType": "time", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "BPM更新时间", "list": "", "maxLength": 40, "metaType": "Time", "name": "bpm_update_time", "remind": "", "required": false, "sort": false, "system": "write", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "更新时间", "control": "", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "update_time", "remind": "", "required": false, "sort": false, "system": "write", "type": "value", "unique": false, "value": "1756276403050", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "五联单扫描件", "column": 12, "control": "file", "dataType": "file", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\n  if (row.file !== undefined && row.file !== null && row.file != '') {\n    var url = 'hwms/api/hwit-paper/'  + row.transfer_id + '?_mode=attachment&bust=' + (new Date()).getTime();\n    return '联单附件：<a href=\"' + url +  '\" target=\"_blank\">点击下载</a>';\n  } else {\n    return '未上传';\n  }\n}", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "file", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "主单据", "column": 6, "control": "text", "dataType": "id", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "master_transfer_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移明细", "control": "", "dataType": "hwms_transfer_detail", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 512, "metaType": "model", "name": "detail", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "出门证", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "out_paper", "remind": "", "required": false, "sort": false, "system": "none", "type": "link", "unique": false, "value": "https://frqas.whchem.com:8043/webroot/decision/view/report?viewlet=whchem/it/ppit/prj/hwms/A1出门证.cpt&transferId=<%=transfer_id%>&__bypagesize__=false", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "出门证（new）", "column": 6, "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "out_paper_new", "remind": "", "required": false, "sort": false, "system": "none", "type": "link", "unique": false, "value": "https://frqas.whchem.com:8043/webroot/decision/view/report?viewlet=whchem/it/ppit/prj/hwms/A1出门证.cpt&transferId=<%=transfer_id%>", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "发送派车信息", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "用于系统判断是否发送派车信息给LMS", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_send_info", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "1", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "年份", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "year", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "2025", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "相关附件", "control": "", "dataType": "hwms_file", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "转移单附件——主要为过磅图片", "list": "", "maxLength": 512, "metaType": "model", "name": "attachment", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属事业部", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "division_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_alias": "", "org_id": "00000016", "org_name": "万华化学（宁波）容威聚氨酯有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}, {"_childList": [{"_childList": [{"org_alias": "", "org_id": "00000439", "org_name": "宁波设备部", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"org_alias": "", "org_id": "00000442", "org_name": "宁波联合装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置大横班乙班", "org_stitle": "", "parent_org_id": "00000436", "status": ""}], "org_alias": "", "org_id": "00000436", "org_name": "宁波MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "生产体系", "org_stitle": "", "parent_org_id": "00000017", "status": ""}], "org_alias": "", "org_id": "00000017", "org_name": "万华化学（宁波）有限公司", "org_stitle": "", "parent_org_id": "", "sort_num": 130, "status": ""}, {"org_alias": "", "org_id": "*******************", "org_name": "仓库管理", "org_stitle": "", "parent_org_id": "00000424", "status": ""}], "count": 8, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "00000017", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "状态", "control": "select", "isRange": false, "name": "bpm_status", "required": false, "value": ""}, {"alias": "申请单号", "control": "text", "isRange": false, "name": "apply_code", "required": false, "value": ""}, {"alias": "五联单号", "control": "text", "isRange": false, "name": "five_bills_code", "required": false, "value": ""}, {"alias": "废物运输车牌号", "control": "chosen", "isRange": false, "name": "car_id", "required": false, "value": ""}, {"alias": "装置", "control": "chosen", "isRange": false, "name": "parent_org_id", "required": false, "value": ""}, {"alias": "申请人", "control": "text", "name": "user_name", "required": false, "value": ""}, {"alias": "申请类型", "control": "select", "isRange": false, "name": "apply_type", "required": false, "value": ""}, {"alias": "车卡", "control": "text", "isRange": false, "name": "card_name", "required": false, "value": ""}, {"alias": "所属公司", "control": "select", "name": "company_id", "required": false, "value": ""}, {"alias": "固废名称", "control": "text", "name": "waste_name", "required": false, "value": ""}, {"alias": "处置去向", "control": "select", "name": "is_sales", "required": false, "value": ""}], "idName": "transfer_id", "nodeIdName": "transfer_id"}, "setting": "", "template": "", "type": "standard"}